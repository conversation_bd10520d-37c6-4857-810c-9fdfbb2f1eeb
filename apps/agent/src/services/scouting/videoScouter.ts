import { TiktokServiceSchema } from '@/schemas/tools_schema';
import { TiktokChallengeSchema, TiktokVideoSchema } from '@repo/constants';
import { TikHubService } from './tikhub';
import { TokAPIService } from './tokapi';
import { scoutDbService, workflowDbService } from '../index';
import { env } from '@/lib/env';
import { extractFirstJson } from '@/lib/utils';
import { ChallengPickerOutputSchema } from '@/agents/challengePickerAgent';
import { Agent, ToolsInput } from '@mastra/core/agent';
import { Metric } from '@mastra/core';

/**
 * Configuration constants for video scouting operations
 * Export for external access and modification
 */
export const SCOUTING_CONFIG = {
  // Challenge selection parameters
  DEFAULT_TOP_N_CHALLENGES: 12,
  DEFAULT_SELECT_K_CHALLENGES: 3,

  // Video fetching parameters
  MAX_CHALLENGE_VIDEOS: 200,
  VIDEOS_PER_API_PAGE: 25,
  CREATOR_POSTS_PER_FETCH: 10,

  // Parallel processing parameters
  MAX_CONCURRENT_JOBS: 5,

  // Filtering parameters
  MAX_VIDEOS_FOR_CONTEXT: 10,
  CREATOR_BATCH_SIZE: 50,
} as const;

/**
 * Extract essential video data for workflow processing (reduce payload size)
 * @param video Full video object
 * @returns Minimal video data with essential fields
 */
function extractVideoForWorkflow(video: TiktokVideoSchema) {
  return {
    video_id: video.video_id,
    description: video.description, // Contains title and hashtags
    view_count: video.view_count,
    like_count: video.like_count,
    comment_count: video.comment_count,
    share_count: video.share_count,
    author: {
      unique_id: video.author.unique_id,
      nickname: video.author.nickname,
      follower_count: video.author.follower_count,
    },
  };
}

/**
 * Service for scouting videos from different platforms and updating the database
 */
export class VideoScouterService {
  private currentService: TiktokServiceSchema;
  private serviceMap: Record<string, TiktokServiceSchema>;

  constructor() {
    // Initialize all available TikTok services
    const tokAPIService = new TokAPIService();
    const tikHubService = new TikHubService();

    this.serviceMap = {
      tokapi: tokAPIService,
      tikhub: tikHubService,
    };

    // Select the service to use
    this.currentService = this.selectService();

    console.log(
      `TikTok service initialized: ${this.currentService.constructor.name}`,
    );
  }

  /**
   * Select the service to use based on preference or default to first available
   * @returns The selected service
   */
  private selectService(): TiktokServiceSchema {
    const preferredService = env.PREFERRED_TIKTOK_SERVICE;

    if (preferredService && this.serviceMap[preferredService]) {
      console.log(
        `Using preferred TikTok service: ${this.serviceMap[preferredService].constructor.name}`,
      );
      return this.serviceMap[preferredService];
    }

    // Use first available service if no preference or preferred not found
    const firstService = Object.values(this.serviceMap)[0];
    const serviceName = firstService.constructor.name;

    if (preferredService && !this.serviceMap[preferredService]) {
      console.warn(
        `Preferred service '${preferredService}' not found, using default: ${serviceName}`,
      );
    } else {
      console.log(
        `No preferred TikTok service configured, using default: ${serviceName}`,
      );
    }

    return firstService;
  }

  /**
   * Execute a service method with error handling and logging
   * @param operation Name of the operation for logging
   * @param serviceMethod The service method to execute
   * @returns The result of the service method
   */
  private async executeServiceMethod<T>(
    operation: string,
    serviceMethod: () => Promise<T>,
  ): Promise<T> {
    const serviceName = this.currentService.constructor.name;

    try {
      console.log(`${operation} with ${serviceName}`);
      const result = await serviceMethod();
      console.log(`Successfully completed ${operation} with ${serviceName}`);
      return result;
    } catch (error) {
      console.error(`Error in ${operation} with ${serviceName}:`, error);
      throw new Error(
        `${operation} failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get information about the current service configuration
   * @returns Service configuration info
   */
  getServiceInfo() {
    const preferredService = env.PREFERRED_TIKTOK_SERVICE;
    const currentServiceName = this.currentService.constructor.name;

    return {
      preferredService: preferredService || 'none',
      availableServices: Object.keys(this.serviceMap),
      currentService: currentServiceName,
      totalServices: Object.keys(this.serviceMap).length,
    };
  }

  /**
   * Search for TikTok videos directly
   * @param keyword The keyword to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   * @param sort_type Sort type (0: relevance, 1: most liked)
   * @param publish_time Publish time (0: unlimited, 1: last 24 hours, 7: past week, 30: past month, 90: past 3 months, 180: past 6 months)
   */
  async searchTiktokVideos(
    keyword: string,
    offset = 0,
    count = 20,
    sort_type = 0,
    publish_time = 0,
  ) {
    return this.executeServiceMethod(
      `Searching TikTok videos for keyword: "${keyword}"`,
      () =>
        this.currentService.searchVideos(
          keyword,
          offset,
          count,
          sort_type,
          publish_time,
        ),
    );
  }

  /**
   * Search for TikTok challenges/hashtags
   * @param keyword The keyword to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   */
  async searchTiktokChallenges(keyword: string, offset = 0, count = 20) {
    return this.executeServiceMethod(
      `Searching TikTok challenges for keyword: "${keyword}"`,
      () => this.currentService.searchHashtag(keyword, offset, count),
    );
  }

  /**
   * Process videos and save them to the database
   * @param videos Array of videos to process
   * @returns Array of processed video IDs
   */
  private async processVideos(videos: TiktokVideoSchema[]): Promise<number[]> {
    const processedVideoIds: number[] = [];

    for (const video of videos) {
      try {
        const videoId = await scoutDbService.processTikTokVideo(video);
        processedVideoIds.push(videoId);
        console.log('Video processed video id:', videoId);
      } catch (error) {
        console.error('Error processing video:', error);
        // Continue with the next video
      }
    }

    return processedVideoIds;
  }

  /**
   * Get videos for a specific TikTok hashtag and update the database
   * @param challengeId The challenge/hashtag ID
   * @param cursor Pagination cursor
   * @param count Number of results to return
   */
  async scoutTiktokHashtagVideos(challengeId: string, cursor = 0, count = 20) {
    // Get videos from the service
    const videos = await this.executeServiceMethod(
      `Getting hashtag videos for challenge ID: "${challengeId}"`,
      () => this.currentService.getHashtagVideos(challengeId, cursor, count),
    );

    // Process videos and save to database
    const processedVideoIds = await this.processVideos(videos);

    console.log(
      `Video processing complete. Processed ${processedVideoIds.length} videos.`,
    );

    return {
      videos,
      processedVideoIds,
    };
  }

  /**
   * Search for challenges based on keywords and find the best ones by view count
   * @param keywords Array of keywords to search for
   * @param traceId Workflow run ID for context tracking
   * @param minChallenges Minimum number of challenges to find
   */
  async findBestChallenges(
    keywords: string[],
    traceId: string,
    minChallenges = 5,
  ) {
    console.log(`Finding best challenges for keywords: ${keywords.join(', ')}`);

    const allChallenges: TiktokChallengeSchema[] = [];

    // Search for challenges for each keyword
    for (const keyword of keywords) {
      try {
        const challenges = await this.searchTiktokChallenges(keyword, 0, 20);
        console.log(
          `Found ${challenges.length} challenges for keyword "${keyword}"`,
        );
        allChallenges.push(...challenges);
      } catch (error) {
        console.error(
          `Error searching challenges for keyword "${keyword}":`,
          error,
        );
      }
    }

    // Sort challenges by view count (descending)
    const sortedChallenges = allChallenges
      .sort((a, b) => b.view_count - a.view_count)
      // Remove duplicates based on challenge_id
      .filter(
        (challenge, index, self) =>
          index ===
          self.findIndex((c) => c.challenge_id === challenge.challenge_id),
      );

    // Take at least minChallenges or all if fewer are available
    const bestChallenges = sortedChallenges.slice(
      0,
      Math.max(minChallenges, sortedChallenges.length),
    );

    console.log(`Selected ${bestChallenges.length} best challenges`);

    // Save to context
    const contextData = {
      keywords,
      challenges: bestChallenges,
      timestamp: new Date().toISOString(),
    };

    await workflowDbService.saveWorkflowContext(
      traceId,
      'best_challenges',
      contextData,
    );

    return bestChallenges;
  }

  /**
   * Fetch videos from the best challenges and collect at least 100 good videos
   * @param traceId Workflow run ID for context tracking
   * @param minVideos Minimum number of videos to collect (default: 100)
   */
  async collectGoodVideos(traceId: string, minVideos = 100) {
    console.log(
      `Collecting at least ${minVideos} good videos for trace ID: ${traceId}`,
    );

    // Get challenges from context
    const contextData = await workflowDbService.getWorkflowContext(
      traceId,
      'best_challenges',
    );

    if (!contextData || !contextData.contextData) {
      throw new Error(
        'No challenges found in context. Run findBestChallenges first.',
      );
    }

    const contextDataObj = contextData.contextData as {
      challenges: TiktokChallengeSchema[];
    };

    if (
      !contextDataObj.challenges ||
      !Array.isArray(contextDataObj.challenges)
    ) {
      throw new Error('Invalid challenge data in context');
    }

    const challenges = contextDataObj.challenges;
    console.log(`Found ${challenges.length} challenges in context`);

    const allVideos: TiktokVideoSchema[] = [];
    const processedVideoIds: number[] = [];

    // Fetch videos from each challenge until we have enough
    for (const challenge of challenges) {
      if (allVideos.length >= minVideos) {
        break;
      }

      try {
        console.log(
          `Fetching videos for challenge: ${challenge.challenge_name}`,
        );
        // Get more videos per challenge to reach our target faster
        const result = await this.scoutTiktokHashtagVideos(
          challenge.challenge_id,
          0,
          25,
        );

        allVideos.push(...result.videos);
        processedVideoIds.push(...result.processedVideoIds);

        console.log(`Collected ${allVideos.length}/${minVideos} videos so far`);
      } catch (error) {
        console.error(
          `Error fetching videos for challenge ${challenge.challenge_name}:`,
          error,
        );
      }
    }

    // Sort videos by view count (descending)
    const sortedVideos = allVideos.sort((a, b) => b.view_count - a.view_count);

    // Save to context
    const contextData2 = {
      challenges,
      videos: sortedVideos.map((video) => ({
        video_id: video.video_id,
        title: video.title,
        view_count: video.view_count,
        like_count: video.like_count,
        comment_count: video.comment_count,
        author: {
          unique_id: video.author.unique_id,
          nickname: video.author.nickname,
        },
      })),
      processedVideoIds,
      timestamp: new Date().toISOString(),
    };

    await workflowDbService.saveWorkflowContext(
      traceId,
      'good_videos',
      contextData2,
    );

    return {
      videos: sortedVideos,
      processedVideoIds,
      totalVideos: sortedVideos.length,
    };
  }

  /**
   * Utility function to process tasks in parallel with a maximum concurrency limit
   * @param tasks Array of async functions to execute
   * @param maxConcurrency Maximum number of concurrent tasks
   * @returns Array of results from Promise.allSettled
   */
  private async processInParallel<T>(
    tasks: (() => Promise<T>)[],
    maxConcurrency = SCOUTING_CONFIG.MAX_CONCURRENT_JOBS,
  ): Promise<PromiseSettledResult<T>[]> {
    const results: PromiseSettledResult<T>[] = [];

    for (let i = 0; i < tasks.length; i += maxConcurrency) {
      const batch = tasks.slice(i, i + maxConcurrency);
      const batchResults = await Promise.allSettled(
        batch.map((task) => task()),
      );
      results.push(...batchResults);

      // Log progress
      console.log(
        `Processed batch ${Math.floor(i / maxConcurrency) + 1}/${Math.ceil(tasks.length / maxConcurrency)}`,
      );
    }

    return results;
  }

  /**
   * Shortcut function to scout creators and their videos from a challenge
   * @param challengeId The challenge/hashtag ID to scout
   * @param desiredCreatorCount Number of unique creators to collect
   * @returns Object containing creators and their videos
   */
  async scoutChallengeCreators(
    challengeId: string,
    desiredCreatorCount: number,
  ) {
    console.log(`Starting challenge scouting for challenge ID: ${challengeId}`);
    console.log(`Target: ${desiredCreatorCount} unique creators`);

    // Step 1: Search challenge videos
    console.log('Step 1: Fetching challenge videos...');
    const challengeVideos: TiktokVideoSchema[] = [];
    let cursor = 0;
    const videosPerPage = SCOUTING_CONFIG.VIDEOS_PER_API_PAGE;
    const maxVideos = SCOUTING_CONFIG.MAX_CHALLENGE_VIDEOS;

    while (challengeVideos.length < maxVideos) {
      try {
        const remainingVideos = maxVideos - challengeVideos.length;
        const countToFetch = Math.min(videosPerPage, remainingVideos);

        console.log(`Fetching videos: cursor=${cursor}, count=${countToFetch}`);
        const videos = await this.executeServiceMethod(
          `Getting hashtag videos batch (cursor: ${cursor})`,
          () =>
            this.currentService.getHashtagVideos(
              challengeId,
              cursor,
              countToFetch,
            ),
        );

        if (videos.length === 0) {
          console.log('No more videos available from API');
          break;
        }

        challengeVideos.push(...videos);
        cursor += videos.length;

        console.log(
          `Collected ${challengeVideos.length}/${maxVideos} challenge videos`,
        );
      } catch (error) {
        console.error(`Error fetching videos at cursor ${cursor}:`, error);
        break;
      }
    }

    console.log(
      `Step 1 complete: Collected ${challengeVideos.length} challenge videos`,
    );

    // Step 2: Extract unique creators
    console.log('Step 2: Extracting unique creators...');
    const creatorMap = new Map<string, TiktokVideoSchema['author']>();

    for (const video of challengeVideos) {
      const creatorId = video.author.sec_uid || video.author.uid;
      if (creatorId && !creatorMap.has(creatorId)) {
        creatorMap.set(creatorId, video.author);
      }
    }

    const uniqueCreators = Array.from(creatorMap.values());
    console.log(`Found ${uniqueCreators.length} unique creators`);

    // Limit to desired count
    const creatorsToProcess = uniqueCreators.slice(0, desiredCreatorCount);
    console.log(
      `Processing ${creatorsToProcess.length} creators (limited to desired count)`,
    );

    // Step 3: Fetch creator posts in parallel
    console.log('Step 3: Fetching creator posts in parallel...');
    const creatorPostTasks = creatorsToProcess.map(
      (creator) => () => this.fetchCreatorPostsWithRetry(creator),
    );

    const creatorPostResults = await this.processInParallel(creatorPostTasks);

    // Process results
    const successfulCreators: Array<{
      creator: TiktokVideoSchema['author'];
      videos: TiktokVideoSchema[];
    }> = [];

    const allCreatorVideos: Array<TiktokVideoSchema> = [];

    for (let i = 0; i < creatorPostResults.length; i++) {
      const result = creatorPostResults[i];
      if (result.status === 'fulfilled' && result.value) {
        const { creator, videos } = result.value;
        successfulCreators.push({ creator, videos });
        allCreatorVideos.push(...videos);
      } else {
        console.error(
          `Failed to fetch posts for creator ${i + 1}:`,
          result.status === 'rejected' ? result.reason : 'Unknown error',
        );
      }
    }

    console.log(
      `Successfully fetched posts for ${successfulCreators.length}/${creatorsToProcess.length} creators`,
    );
    console.log(`Total creator videos collected: ${allCreatorVideos.length}`);

    // Step 4: Process and persist all videos
    console.log('Step 4: Processing and persisting videos...');
    const allVideos = [...challengeVideos, ...allCreatorVideos];
    const processedVideoIds = await this.processVideos(allVideos);

    console.log(`Persisted ${processedVideoIds.length} videos to database`);

    // Step 5: Prepare final results with reliable creator data
    console.log('Step 5: Preparing final results...');
    const finalCreators = successfulCreators.map(({ creator, videos }) => {
      // Use creator data from the first video post (more reliable)
      const reliableCreatorData =
        videos.length > 0 ? videos[0].author : creator;

      return {
        ...reliableCreatorData,
        videoCount: videos.length,
      };
    });

    // Optimize video data for workflow processing (reduce payload size)
    const optimizedVideos = allCreatorVideos.map(extractVideoForWorkflow);

    const result = {
      creators: finalCreators,
      videos: optimizedVideos, // Return optimized video data for workflow
      stats: {
        challengeVideosCollected: challengeVideos.length,
        uniqueCreatorsFound: uniqueCreators.length,
        creatorsProcessed: creatorsToProcess.length,
        successfulCreators: successfulCreators.length,
        totalCreatorVideos: allCreatorVideos.length,
        totalVideosProcessed: processedVideoIds.length,
      },
    };

    console.log('Challenge scouting complete!');
    console.log('Final stats:', result.stats);

    return result;
  }

  /**
   * Helper function to fetch creator posts with retry logic
   * @param creator The creator to fetch posts for
   * @returns Object with creator and their videos
   */
  private async fetchCreatorPostsWithRetry(
    creator: TiktokVideoSchema['author'],
  ): Promise<{
    creator: TiktokVideoSchema['author'];
    videos: TiktokVideoSchema[];
  }> {
    const userId = creator.sec_uid || creator.uid;
    if (!userId) {
      throw new Error(
        `No valid user ID found for creator: ${creator.unique_id}`,
      );
    }

    try {
      console.log(
        `Fetching posts for creator: ${creator.unique_id} (${creator.nickname})`,
      );

      const videos = await this.executeServiceMethod(
        `Getting creator posts for: ${creator.unique_id}`,
        () =>
          this.currentService.getCreatorPosts(
            userId,
            0,
            SCOUTING_CONFIG.CREATOR_POSTS_PER_FETCH,
          ),
      );

      console.log(
        `Fetched ${videos.length} posts for creator: ${creator.unique_id}`,
      );

      return { creator, videos };
    } catch (error) {
      console.error(
        `Error fetching posts for creator ${creator.unique_id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Select challenges from keywords using either random or intelligent selection
   * @param keywords Array of keywords to search challenges for
   * @param options Configuration options for challenge selection
   * @returns Array of selected challenge IDs
   */
  async selectChallengesFromKeywords(
    keywords: string[],
    options: {
      useIntelligentSelection?: boolean;
      topN?: number;
      selectK?: number;
      targetCreatorDescription?: string;
      scoutGuidance?: string;
      agent?: Agent<any, ToolsInput, Record<string, Metric>>;
    } = {},
  ) {
    const {
      useIntelligentSelection = false,
      topN = SCOUTING_CONFIG.DEFAULT_TOP_N_CHALLENGES,
      selectK = SCOUTING_CONFIG.DEFAULT_SELECT_K_CHALLENGES,
      targetCreatorDescription,
      scoutGuidance,
      agent,
    } = options;

    console.log(`Selecting challenges from keywords: ${keywords.join(', ')}`);
    console.log(
      `Selection mode: ${useIntelligentSelection ? 'Intelligent' : 'Random'}`,
    );
    console.log(`Top N: ${topN}, Select K: ${selectK}`);

    // Step 1: Search for challenges for all keywords
    const allChallenges: TiktokChallengeSchema[] = [];

    for (const keyword of keywords) {
      try {
        const sanitizedKeyword = keyword.replace(/#/g, '').trim();
        console.log(`Searching challenges for keyword: "${sanitizedKeyword}"`);

        const challenges = await this.searchTiktokChallenges(
          sanitizedKeyword,
          0,
          20,
        );
        console.log(
          `Found ${challenges.length} challenges for keyword "${sanitizedKeyword}"`,
        );

        allChallenges.push(...challenges);
      } catch (error) {
        console.error(
          `Error searching challenges for keyword "${keyword}":`,
          error,
        );
        // Continue with next keyword
      }
    }

    // Step 2: Deduplicate and sort by view count
    const uniqueChallenges = allChallenges
      .filter(
        (challenge, index, self) =>
          index ===
          self.findIndex((c) => c.challenge_id === challenge.challenge_id),
      )
      .sort((a, b) => b.view_count - a.view_count);

    console.log(`Found ${uniqueChallenges.length} unique challenges total`);

    // Step 3: Take top N challenges
    const topChallenges = uniqueChallenges.slice(0, topN);
    console.log(
      `Selected top ${topChallenges.length} challenges by view count`,
    );

    if (topChallenges.length === 0) {
      console.warn('No challenges found for the given keywords');
      return [];
    }

    // Step 4: Select K challenges using the specified method
    let selectedChallenges: TiktokChallengeSchema[];

    if (
      useIntelligentSelection &&
      agent &&
      targetCreatorDescription &&
      scoutGuidance
    ) {
      console.log('Using intelligent challenge selection...');
      selectedChallenges = await this.selectChallengesIntelligently(
        topChallenges,
        targetCreatorDescription,
        scoutGuidance,
        selectK,
        agent,
      );
    } else {
      console.log('Using random challenge selection...');
      selectedChallenges = this.selectChallengesRandomly(
        topChallenges,
        selectK,
      );
    }

    console.log(`Final selection: ${selectedChallenges.length} challenges`);
    selectedChallenges.forEach((challenge, index) => {
      console.log(
        `${index + 1}. ${challenge.challenge_name} (ID: ${challenge.challenge_id}, Views: ${challenge.view_count})`,
      );
    });

    return selectedChallenges.map((c) => c.challenge_id);
  }

  /**
   * Select challenges randomly from the top challenges
   * @param challenges Array of challenges to select from
   * @param selectK Number of challenges to select
   * @returns Array of selected challenges
   */
  private selectChallengesRandomly(
    challenges: TiktokChallengeSchema[],
    selectK: number,
  ): TiktokChallengeSchema[] {
    const shuffled = [...challenges].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, Math.min(selectK, challenges.length));
  }

  /**
   * Select challenges intelligently using the challenge picker agent
   * @param challenges Array of challenges to select from
   * @param targetCreatorDescription Description of target creators
   * @param scoutGuidance Additional guidance from the scout agent
   * @param selectK Number of challenges to select
   * @param mastraInstance Mastra instance to get the agent
   * @returns Array of selected challenges
   */
  private async selectChallengesIntelligently(
    challenges: TiktokChallengeSchema[],
    targetCreatorDescription: string,
    scoutGuidance: string,
    selectK: number,
    pickerAgent: Agent<any, ToolsInput, Record<string, Metric>>,
  ): Promise<TiktokChallengeSchema[]> {
    try {
      if (!pickerAgent) {
        console.warn(
          'Challenge picker agent not found, falling back to random selection',
        );
        return this.selectChallengesRandomly(challenges, selectK);
      }

      const prompt = `Challenge Requirements: ${targetCreatorDescription}

      Scout Guidance: ${scoutGuidance}

Please select the ${selectK} most relevant challenges from the following options:

Challenge Data: ${JSON.stringify(challenges, null, 2)}`;

      const userMessage = {
        role: 'user' as const,
        content: prompt,
      };

      const resp = await pickerAgent.generate([userMessage], {
        output: ChallengPickerOutputSchema,
      });
      // const assistantMessage = resp.response.messages[0];
      // const content = (assistantMessage.content as Array<{ text: string }>)[0];
      // const parsedResult = extractFirstJson(content.text);

      if (
        parsedResult &&
        parsedResult.selectedChallenges &&
        Array.isArray(parsedResult.selectedChallenges)
      ) {
        const selectedIds = parsedResult.selectedChallenges.map(
          (item: any) => item.challenge_id,
        );
        const selectedChallenges = challenges.filter((c) =>
          selectedIds.includes(c.challenge_id),
        );

        if (selectedChallenges.length > 0) {
          console.log('Successfully selected challenges using AI agent');
          return selectedChallenges.slice(0, selectK);
        }
      }

      console.warn(
        'Failed to parse AI agent response, falling back to random selection',
      );
      return this.selectChallengesRandomly(challenges, selectK);
    } catch (error) {
      console.error('Error in intelligent challenge selection:', error);
      console.log('Falling back to random selection');
      return this.selectChallengesRandomly(challenges, selectK);
    }
  }
}

// Export a singleton instance
export const videoScouter = new VideoScouterService();
